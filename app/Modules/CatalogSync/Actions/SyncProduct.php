<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Actions;

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\CatalogSync\Data\ProductOffer as DataProductOffer;
use App\Modules\Product\Models\ProductBrand;
use App\Modules\Product\Models\ProductManufacturer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Laravel\Nightwatch\Facades\Nightwatch;

final class SyncProduct
{
    public function handle(Clinic $clinic, Vendor $vendor, DataProductOffer $data): void
    {
        Nightwatch::sample(rate: config('highfive.nightwatch_custom_rate'));

        $productOffer = ProductOffer::query()
            ->withTrashed()
            ->updateOrCreate([
                'vendor_id' => $vendor->id,
                'vendor_sku' => $data->sku,
            ], [
                'name' => mb_substr($data->name, 0, 255),
                'price' => $data->pricing->listPriceInCents,
                'stock_status' => $data->stockStatus,
                'increments' => $data->increments,
                'external_id' => $data->id,
                'deactivated_at' => null,
                'last_synced_at' => now(),
                'deleted_at' => null,
                'size' => $data->size,
                'unit_of_measure' => $data->unitOfMeasure,
                'raw_category_1' => $data->rawCategory1,
                'raw_category_2' => $data->rawCategory2,
                'raw_category_3' => $data->rawCategory3,
                'raw_category_4' => $data->rawCategory4,
            ]);

        $product = Product::withoutSyncingToSearch(function () use ($data, $productOffer) {
            if ($productOffer->wasRecentlyCreated) {
                $product = Product::create([
                    'name' => mb_substr($data->name, 0, 255),
                    'description' => $data->description,
                    'image_url' => mb_substr($data->image ?? '', 0, 255),
                    'manufacturer' => $data->manufacturer,
                    'manufacturer_sku' => $data->manufacturerSku,
                    'sku' => $data->sku,
                    'category' => $data->category,
                    'is_hazardous' => $data->flags->isHazardous,
                    'requires_prescription' => $data->flags->requiresPrescription,
                    'requires_cold_shipping' => $data->flags->requiresColdShipping,
                    'is_controlled_substance' => $data->flags->isControlledSubstance,
                    'is_controlled_222_form' => $data->flags->isControlled222Form,
                    'requires_pedigree' => $data->flags->requiresPedigree,
                    'national_drug_code' => $data->nationalDrugCode,
                ]);

                $productOffer->update(['product_id' => $product->id]);
            } else {
                $product = $productOffer->product;

                if (! $product->national_drug_code && $data->nationalDrugCode) {
                    $product->national_drug_code = $data->nationalDrugCode;
                    $product->save();
                }
            }

            return $product;
        });

        $this->syncProductBrand($productOffer, $data->brand);

        $this->syncProductManufacturer($productOffer, $data->manufacturer);

        $this->syncProductAttributes($product, $data->attributes);

        $this->syncClinicPrice($clinic, $productOffer, $data->pricing->clinicPriceInCents);
    }

    private function syncProductBrand(ProductOffer $offer, ?string $name): void
    {
        if (is_null($offer->product_id) || is_null($name)) {
            return;
        }

        $brand = ProductBrand::query()
            ->whereHas('mappings', function (Builder $query) use ($name) {
                $query->where('name', $name);
            })
            ->firstOrCreate([], ['name' => $name]);

        DB::table('products')
            ->where('id', $offer->product_id)
            ->update([
                'product_brand_id' => $brand->id,
            ]);
    }

    private function syncProductManufacturer(ProductOffer $offer, ?string $name): void
    {
        if (is_null($offer->product_id) || is_null($name)) {
            return;
        }

        $manufacturer = ProductManufacturer::query()
            ->whereHas('mappings', function (Builder $query) use ($name) {
                $query->where('name', $name);
            })
            ->firstOrCreate([], ['name' => $name]);

        DB::table('products')
            ->where('id', $offer->product_id)
            ->update([
                'product_manufacturer_id' => $manufacturer->id,
            ]);
    }

    private function syncProductAttributes(Product $product, array $attributes): void
    {
        $existingAttributeNames = $product->attributes()->pluck('name')->toArray();
        $newAttributeNames = array_keys($attributes);

        $attributesToDelete = array_diff($existingAttributeNames, $newAttributeNames);
        if (! empty($attributesToDelete)) {
            $product->attributes()->whereIn('name', $attributesToDelete)->delete();
        }

        foreach ($attributes as $name => $value) {
            $product->attributes()->updateOrCreate(
                ['name' => $name],
                ['value' => mb_substr($value, 0, 255)]
            );
        }
    }

    private function syncClinicPrice(Clinic $clinic, ProductOffer $product, ?int $price): void
    {
        if (is_null($price)) {
            DB::table('clinic_product_offer')
                ->where('clinic_id', $clinic->id)
                ->where('product_offer_id', $product->id)
                ->delete();

            return;
        }

        DB::table('clinic_product_offer')
            ->updateOrInsert(
                [
                    'clinic_id' => $clinic->id,
                    'product_offer_id' => $product->id,
                ],
                [
                    'price' => $price,
                ]
            );
    }
}
